import { <PERSON><PERSON>, Card, CardContent, CardDescription, CardHeader, CardTitle, Input } from "@stackshift-ui/shadcn";
import type { FC } from "react";

/**
 * Example component demonstrating how to use shadcn/ui components
 * within your existing component packages
 */
export const ShadcnExample: FC = () => {
  return (
    <Card className="w-[350px]">
      <CardHeader>
        <CardTitle>shadcn/ui Integration</CardTitle>
        <CardDescription>
          This demonstrates how to use shadcn/ui components in your existing packages.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid w-full items-center gap-4">
          <div className="flex flex-col space-y-1.5">
            <label htmlFor="name">Name</label>
            <Input id="name" placeholder="Enter your name" />
          </div>
          <div className="flex justify-between">
            <Button variant="outline">Cancel</Button>
            <Button>Submit</Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
